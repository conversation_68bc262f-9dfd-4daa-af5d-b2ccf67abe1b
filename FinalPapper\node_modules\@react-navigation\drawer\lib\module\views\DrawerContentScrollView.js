"use strict";

import { useLocale } from '@react-navigation/native';
import * as React from 'react';
import { ScrollView, StyleSheet } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { DrawerPositionContext } from "../utils/DrawerPositionContext.js";
import { jsx as _jsx } from "react/jsx-runtime";
const SPACING = 12;
function DrawerContentScrollViewInner({
  contentContainerStyle,
  style,
  children,
  ...rest
}, ref) {
  const drawerPosition = React.useContext(DrawerPositionContext);
  const insets = useSafeAreaInsets();
  const {
    direction
  } = useLocale();
  const isRight = direction === 'rtl' ? drawerPosition === 'left' : drawerPosition === 'right';
  return /*#__PURE__*/_jsx(ScrollView, {
    ...rest,
    ref: ref,
    contentContainerStyle: [{
      paddingTop: SPACING + insets.top,
      paddingBottom: SPACING + insets.bottom,
      paddingStart: SPACING + (!isRight ? insets.left : 0),
      paddingEnd: SPACING + (isRight ? insets.right : 0)
    }, contentContainerStyle],
    style: [styles.container, style],
    children: children
  });
}
export const DrawerContentScrollView = /*#__PURE__*/React.forwardRef(DrawerContentScrollViewInner);
const styles = StyleSheet.create({
  container: {
    flex: 1
  }
});
//# sourceMappingURL=DrawerContentScrollView.js.map