import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
} from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

const DetailScreen: React.FC = () => {
  const params = useLocalSearchParams();
  const router = useRouter();
  
  const { name, age, section, registrationId } = params;

  const handleGoBack = () => {
    router.back();
  };

  return (
    <ScrollView style={styles.container}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={handleGoBack}>
          <Ionicons name="arrow-back" size={24} color="#007AFF" />
        </TouchableOpacity>
        <Text style={styles.title}>Student Details</Text>
      </View>

      <View style={styles.card}>
        <View style={styles.cardHeader}>
          <Ionicons name="person-circle" size={60} color="#007AFF" />
          <Text style={styles.cardTitle}>Personal Information</Text>
        </View>

        <View style={styles.detailsContainer}>
          <View style={styles.detailRow}>
            <View style={styles.labelContainer}>
              <Ionicons name="person" size={20} color="#666" />
              <Text style={styles.label}>Full Name:</Text>
            </View>
            <Text style={styles.value}>{name}</Text>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.labelContainer}>
              <Ionicons name="calendar" size={20} color="#666" />
              <Text style={styles.label}>Age:</Text>
            </View>
            <Text style={styles.value}>{age} years old</Text>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.labelContainer}>
              <Ionicons name="school" size={20} color="#666" />
              <Text style={styles.label}>Section:</Text>
            </View>
            <Text style={styles.value}>{section}</Text>
          </View>

          <View style={styles.detailRow}>
            <View style={styles.labelContainer}>
              <Ionicons name="card" size={20} color="#666" />
              <Text style={styles.label}>Registration ID:</Text>
            </View>
            <Text style={styles.value}>{registrationId}</Text>
          </View>
        </View>

        <View style={styles.footer}>
          <Text style={styles.footerText}>
            Information submitted successfully
          </Text>
          <Ionicons name="checkmark-circle" size={24} color="#28a745" />
        </View>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    paddingTop: 60,
    backgroundColor: 'white',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  backButton: {
    marginRight: 15,
    padding: 5,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#333',
  },
  card: {
    backgroundColor: 'white',
    margin: 20,
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 8,
  },
  cardHeader: {
    alignItems: 'center',
    marginBottom: 30,
  },
  cardTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: '#333',
    marginTop: 10,
  },
  detailsContainer: {
    marginBottom: 30,
  },
  detailRow: {
    marginBottom: 20,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    fontWeight: '600',
    color: '#666',
    marginLeft: 8,
  },
  value: {
    fontSize: 18,
    fontWeight: '500',
    color: '#333',
    marginLeft: 28,
  },
  footer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  footerText: {
    fontSize: 16,
    color: '#28a745',
    fontWeight: '500',
    marginRight: 8,
  },
});

export default DetailScreen;
