{"version": 3, "names": ["CardStyleInterpolators", "HeaderStyleInterpolators", "TransitionPresets", "TransitionSpecs", "createStackNavigator", "Header", "StackView", "CardAnimationContext", "GestureHandlerRefContext", "useCardAnimation", "useGestureHandlerRef"], "sourceRoot": "../../src", "sources": ["index.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,sBAAsB,MAAM,+CAA4C;AACpF,OAAO,KAAKC,wBAAwB,MAAM,iDAA8C;AACxF,OAAO,KAAKC,iBAAiB,MAAM,0CAAuC;AAC1E,OAAO,KAAKC,eAAe,MAAM,wCAAqC;;AAEtE;AACA;AACA;AACA,SAASC,oBAAoB,QAAQ,sCAAmC;;AAExE;AACA;AACA;AACA,SAASC,MAAM,QAAQ,0BAAuB;AAC9C,SAASC,SAAS,QAAQ,4BAAyB;;AAEnD;AACA;AACA;AACA,SACEN,sBAAsB,EACtBC,wBAAwB,EACxBC,iBAAiB,EACjBC,eAAe;;AAGjB;AACA;AACA;AACA,SAASI,oBAAoB,QAAQ,iCAA8B;AACnE,SAASC,wBAAwB,QAAQ,qCAAkC;AAC3E,SAASC,gBAAgB,QAAQ,6BAA0B;AAC3D,SAASC,oBAAoB,QAAQ,iCAA8B;;AAEnE;AACA;AACA", "ignoreList": []}