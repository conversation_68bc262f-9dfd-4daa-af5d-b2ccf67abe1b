{"version": 3, "names": ["React", "DrawerProgressContext", "useDrawerProgress", "progress", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useDrawerProgress.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAG9B,SAASC,qBAAqB,QAAQ,4BAAyB;AAE/D,OAAO,SAASC,iBAAiBA,CAAA,EAAkC;EACjE,MAAMC,QAAQ,GAAGH,KAAK,CAACI,UAAU,CAACH,qBAAqB,CAAC;EAExD,IAAIE,QAAQ,KAAKE,SAAS,EAAE;IAC1B,MAAM,IAAIC,KAAK,CACb,4DACF,CAAC;EACH;EAEA,OAAOH,QAAQ;AACjB", "ignoreList": []}