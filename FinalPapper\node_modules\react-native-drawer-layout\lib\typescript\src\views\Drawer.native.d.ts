import type { DrawerProps } from '../types';
export declare function Drawer({ layout: customLayout, direction, drawerPosition, drawerStyle, drawerType, configureGestureHandler, hideStatusBarOnOpen, keyboardDismissMode, onClose, onOpen, onGestureStart, onGestureCancel, onGestureEnd, onTransitionStart, onTransitionEnd, open, overlayStyle, overlayAccessibilityLabel, statusBarAnimation, swipeEnabled, swipeEdgeWidth, swipeMinDistance, swipeMinVelocity, renderDrawerContent, children, style, }: DrawerProps): import("react/jsx-runtime").JSX.Element;
//# sourceMappingURL=Drawer.native.d.ts.map