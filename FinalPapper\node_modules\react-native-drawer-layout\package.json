{"name": "react-native-drawer-layout", "description": "Drawer component for React Native", "version": "4.1.11", "keywords": ["react-native-component", "react-component", "react-native", "ios", "android", "drawer", "swipe"], "license": "MIT", "repository": {"type": "git", "url": "https://github.com/react-navigation/react-navigation.git", "directory": "packages/react-native-drawer-layout"}, "bugs": {"url": "https://github.com/react-navigation/react-navigation/issues"}, "homepage": "https://reactnavigation.org/docs/drawer-layout/", "main": "./lib/module/index.js", "types": "./lib/typescript/src/index.d.ts", "exports": {".": {"source": "./src/index.tsx", "types": "./lib/typescript/src/index.d.ts", "default": "./lib/module/index.js"}, "./package.json": "./package.json"}, "files": ["src", "lib", "!**/__tests__"], "sideEffects": false, "scripts": {"prepack": "bob build", "clean": "del lib"}, "dependencies": {"use-latest-callback": "^0.2.4"}, "devDependencies": {"del-cli": "^6.0.0", "react": "19.0.0", "react-native": "0.79.3", "react-native-builder-bob": "^0.40.12", "typescript": "^5.8.3"}, "peerDependencies": {"react": ">= 18.2.0", "react-native": "*", "react-native-gesture-handler": ">= 2.0.0", "react-native-reanimated": ">= 2.0.0"}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["module", {"esm": true}], ["typescript", {"project": "tsconfig.build.json"}]]}, "gitHead": "27560ad2fc55987654a2f9419a016225387e78fe"}