import React from 'react';
import { Stack } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';

const AuthNavigator: React.FC = () => {
  const { isAuthenticated } = useAuth();

  return (
    <Stack screenOptions={{ headerShown: false }}>
      {!isAuthenticated ? (
        // Authentication stack
        <>
          <Stack.Screen name="login" options={{ headerShown: false }} />
        </>
      ) : (
        // Main app stack
        <>
          <Stack.Screen name="(drawer)" options={{ headerShown: false }} />
          <Stack.Screen 
            name="detail" 
            options={{ 
              headerShown: false,
              presentation: 'modal'
            }} 
          />
        </>
      )}
      <Stack.Screen name="+not-found" />
    </Stack>
  );
};

export default AuthNavigator;
