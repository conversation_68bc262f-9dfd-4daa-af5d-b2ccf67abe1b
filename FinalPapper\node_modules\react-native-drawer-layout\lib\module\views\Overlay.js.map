{"version": 3, "names": ["Pressable", "StyleSheet", "View", "jsx", "_jsx", "Overlay", "open", "onPress", "style", "accessibilityLabel", "rest", "styles", "overlay", "opacity", "pointerEvents", "children", "pressable", "role", "create", "absoluteFillObject", "backgroundColor", "WebkitTapHighlightColor", "transition", "flex"], "sourceRoot": "../../../src", "sources": ["views/Overlay.tsx"], "mappings": ";;AAAA,SAASA,SAAS,EAAEC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAI3D,OAAO,SAASC,OAAOA,CAAC;EACtBC,IAAI;EACJC,OAAO;EACPC,KAAK;EACLC,kBAAkB,GAAG,cAAc;EACnC,GAAGC;AACS,CAAC,EAAE;EACf,oBACEN,IAAA,CAACF,IAAI;IAAA,GACCQ,IAAI;IACRF,KAAK,EAAE,CACLG,MAAM,CAACC,OAAO,EACd;MAAEC,OAAO,EAAEP,IAAI,GAAG,CAAC,GAAG,CAAC;MAAEQ,aAAa,EAAER,IAAI,GAAG,MAAM,GAAG;IAAO,CAAC,EAChEE,KAAK,CACL;IACF,eAAa,CAACF,IAAK;IAAAS,QAAA,eAEnBX,IAAA,CAACJ,SAAS;MACRO,OAAO,EAAEA,OAAQ;MACjBC,KAAK,EAAE,CAACG,MAAM,CAACK,SAAS,EAAE;QAAEF,aAAa,EAAER,IAAI,GAAG,MAAM,GAAG;MAAO,CAAC,CAAE;MACrEW,IAAI,EAAC,QAAQ;MACb,cAAYR;IAAmB,CAChC;EAAC,CACE,CAAC;AAEX;AAEA,MAAME,MAAM,GAAGV,UAAU,CAACiB,MAAM,CAAC;EAC/BN,OAAO,EAAE;IACP,GAAGX,UAAU,CAACkB,kBAAkB;IAChCC,eAAe,EAAE,oBAAoB;IACrC;IACA;IACA;IACAC,uBAAuB,EAAE,aAAa;IACtCC,UAAU,EAAE;EACd,CAAC;EACDN,SAAS,EAAE;IACTO,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}