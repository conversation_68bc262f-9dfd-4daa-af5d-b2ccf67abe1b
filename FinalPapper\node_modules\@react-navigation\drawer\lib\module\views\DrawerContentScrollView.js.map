{"version": 3, "names": ["useLocale", "React", "ScrollView", "StyleSheet", "useSafeAreaInsets", "DrawerPositionContext", "jsx", "_jsx", "SPACING", "DrawerContentScrollViewInner", "contentContainerStyle", "style", "children", "rest", "ref", "drawerPosition", "useContext", "insets", "direction", "isRight", "paddingTop", "top", "paddingBottom", "bottom", "paddingStart", "left", "paddingEnd", "right", "styles", "container", "DrawerContentScrollView", "forwardRef", "create", "flex"], "sourceRoot": "../../../src", "sources": ["views/DrawerContentScrollView.tsx"], "mappings": ";;AAAA,SAASA,SAAS,QAAQ,0BAA0B;AACpD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAwBC,UAAU,QAAQ,cAAc;AAC3E,SAASC,iBAAiB,QAAQ,gCAAgC;AAElE,SAASC,qBAAqB,QAAQ,mCAAgC;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAMvE,MAAMC,OAAO,GAAG,EAAE;AAElB,SAASC,4BAA4BA,CACnC;EAAEC,qBAAqB;EAAEC,KAAK;EAAEC,QAAQ;EAAE,GAAGC;AAAY,CAAC,EAC1DC,GAA2B,EAC3B;EACA,MAAMC,cAAc,GAAGd,KAAK,CAACe,UAAU,CAACX,qBAAqB,CAAC;EAC9D,MAAMY,MAAM,GAAGb,iBAAiB,CAAC,CAAC;EAClC,MAAM;IAAEc;EAAU,CAAC,GAAGlB,SAAS,CAAC,CAAC;EAEjC,MAAMmB,OAAO,GACXD,SAAS,KAAK,KAAK,GACfH,cAAc,KAAK,MAAM,GACzBA,cAAc,KAAK,OAAO;EAEhC,oBACER,IAAA,CAACL,UAAU;IAAA,GACLW,IAAI;IACRC,GAAG,EAAEA,GAAI;IACTJ,qBAAqB,EAAE,CACrB;MACEU,UAAU,EAAEZ,OAAO,GAAGS,MAAM,CAACI,GAAG;MAChCC,aAAa,EAAEd,OAAO,GAAGS,MAAM,CAACM,MAAM;MACtCC,YAAY,EAAEhB,OAAO,IAAI,CAACW,OAAO,GAAGF,MAAM,CAACQ,IAAI,GAAG,CAAC,CAAC;MACpDC,UAAU,EAAElB,OAAO,IAAIW,OAAO,GAAGF,MAAM,CAACU,KAAK,GAAG,CAAC;IACnD,CAAC,EACDjB,qBAAqB,CACrB;IACFC,KAAK,EAAE,CAACiB,MAAM,CAACC,SAAS,EAAElB,KAAK,CAAE;IAAAC,QAAA,EAEhCA;EAAQ,CACC,CAAC;AAEjB;AAEA,OAAO,MAAMkB,uBAAuB,gBAAG7B,KAAK,CAAC8B,UAAU,CACrDtB,4BACF,CAAC;AAED,MAAMmB,MAAM,GAAGzB,UAAU,CAAC6B,MAAM,CAAC;EAC/BH,SAAS,EAAE;IACTI,IAAI,EAAE;EACR;AACF,CAAC,CAAC", "ignoreList": []}