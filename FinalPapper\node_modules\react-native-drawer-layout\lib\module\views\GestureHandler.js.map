{"version": 3, "names": ["React", "View", "Fragment", "_Fragment", "jsx", "_jsx", "GestureDetector", "gesture", "_0", "userSelect", "_1", "children", "GestureHandlerRootView", "Gesture", "undefined", "GestureState"], "sourceRoot": "../../../src", "sources": ["views/GestureHandler.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,IAAI,QAAQ,cAAc;;AAGnC;AACA;AACA;AAAA,SAAAC,QAAA,IAAAC,SAAA,EAAAC,GAAA,IAAAC,IAAA;AAOA,OAAO,MAAMC,eAAe,GAAGA,CAAC;EAC9BC,OAAO,EAAEC,EAAE;EACXC,UAAU,EAAEC,EAAE;EACdC;AACoB,CAAC,KAAK;EAC1B,oBAAON,IAAA,CAAAF,SAAA;IAAAQ,QAAA,EAAGA;EAAQ,CAAG,CAAC;AACxB,CAAC;AAED,OAAO,MAAMC,sBAAsB,GAAGX,IAAI;AAE1C,OAAO,MAAMY,OAEA,GAAGC,SAAS;AAEzB,WAAkBC,YAAY,0BAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAZA,YAAY,CAAZA,YAAY;EAAA,OAAZA,YAAY;AAAA", "ignoreList": []}