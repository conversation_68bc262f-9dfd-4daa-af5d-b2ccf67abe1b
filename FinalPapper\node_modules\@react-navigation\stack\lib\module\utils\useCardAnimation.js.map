{"version": 3, "names": ["React", "CardAnimationContext", "useCardAnimation", "animation", "useContext", "undefined", "Error"], "sourceRoot": "../../../src", "sources": ["utils/useCardAnimation.tsx"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,SAASC,oBAAoB,QAAQ,2BAAwB;AAE7D,OAAO,SAASC,gBAAgBA,CAAA,EAAG;EACjC,MAAMC,SAAS,GAAGH,KAAK,CAACI,UAAU,CAACH,oBAAoB,CAAC;EAExD,IAAIE,SAAS,KAAKE,SAAS,EAAE;IAC3B,MAAM,IAAIC,KAAK,CACb,4EACF,CAAC;EACH;EAEA,OAAOH,SAAS;AAClB", "ignoreList": []}