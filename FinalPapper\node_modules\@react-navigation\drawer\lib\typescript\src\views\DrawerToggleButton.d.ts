import { type ImageSourcePropType } from 'react-native';
type Props = {
    accessibilityLabel?: string;
    pressColor?: string;
    pressOpacity?: number;
    tintColor?: string;
    imageSource?: ImageSourcePropType;
};
export declare function DrawerToggleButton({ tintColor, accessibilityLabel, imageSource, ...rest }: Props): import("react/jsx-runtime").JSX.Element;
export {};
//# sourceMappingURL=DrawerToggleButton.d.ts.map