# React Native Drawer Layout

A cross-platform Drawer component for React Native implemented using [`react-native-gesture-handler`](https://docs.swmansion.com/react-native-gesture-handler/) and [`react-native-reanimated`](https://docs.swmansion.com/react-native-reanimated/) on native platforms and CSS transitions on Web.

Installation instructions and documentation can be found on the [React Navigation website](https://reactnavigation.org/docs/drawer-layout/).
