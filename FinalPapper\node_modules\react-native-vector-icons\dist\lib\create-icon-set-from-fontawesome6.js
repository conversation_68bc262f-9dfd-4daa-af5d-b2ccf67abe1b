var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.FA6Style=void 0;exports.createFA6iconSet=createFA6iconSet;var _reactNative=require("react-native");var _createMultiStyleIconSet=_interopRequireDefault(require("./create-multi-style-icon-set"));var FA6Style=exports.FA6Style={regular:'regular',light:'light',solid:'solid',brand:'brand',sharp:'sharp',sharpLight:'sharpLight',sharpSolid:'sharpSolid',duotone:'duotone',thin:'thin'};function createFA6iconSet(glyphMap){var metadata=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};var pro=arguments.length>2&&arguments[2]!==undefined?arguments[2]:false;var metadataKeys=Object.keys(metadata);var fontFamily="FontAwesome6"+(pro?'Pro':'Free');function fallbackFamily(glyph){for(var i=0;i<metadataKeys.length;i+=1){var family=metadataKeys[i];if(metadata[family].indexOf(glyph)!==-1){return family==='brands'?'brand':family;}}return'regular';}function glyphValidator(glyph,style){var family=style==='brand'?'brands':style;family=style==='sharpSolid'?'sharp-solid':family;if(metadataKeys.indexOf(family)===-1)return false;return metadata[family].indexOf(glyph)!==-1;}function createFontAwesomeStyle(style,fontWeight){var family=arguments.length>2&&arguments[2]!==undefined?arguments[2]:fontFamily;var styleName=style;var fontFile="FontAwesome6_"+(pro?"Pro_"+styleName:styleName)+".ttf";if(styleName==='Brands'){styleName='Regular';}if(styleName==='Duotone'){styleName='Solid';}styleName=styleName.replace('Sharp_','');return{fontFamily:family+"-"+styleName,fontFile:fontFile,fontStyle:_reactNative.Platform.select({ios:{fontWeight:fontWeight},default:{}}),glyphMap:glyphMap};}var brandIcons=createFontAwesomeStyle('Brands','400','FontAwesome6Brands');var lightIcons=createFontAwesomeStyle('Light','300');var regularIcons=createFontAwesomeStyle('Regular','400');var solidIcons=createFontAwesomeStyle('Solid','900');var sharpLightIcons=createFontAwesomeStyle('Sharp_Light','300','FontAwesome6Sharp');var sharpIcons=createFontAwesomeStyle('Sharp_Regular','400','FontAwesome6Sharp');var sharpSolidIcons=createFontAwesomeStyle('Sharp_Solid','900','FontAwesome6Sharp');var duotoneIcons=createFontAwesomeStyle('Duotone','900','FontAwesome6Duotone');var thinIcons=createFontAwesomeStyle('Thin','100');var Icon=(0,_createMultiStyleIconSet.default)({brand:brandIcons,light:lightIcons,regular:regularIcons,solid:solidIcons,sharp:sharpIcons,sharpLight:sharpLightIcons,sharpSolid:sharpSolidIcons,duotone:duotoneIcons,thin:thinIcons},{defaultStyle:'regular',fallbackFamily:fallbackFamily,glyphValidator:glyphValidator});return Icon;}