{"version": 3, "names": ["createNavigatorFactory", "DrawerRouter", "useNavigationBuilder", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "jsx", "_jsx", "Drawer<PERSON><PERSON><PERSON><PERSON>", "id", "initialRouteName", "defaultStatus", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "children", "layout", "screenListeners", "screenOptions", "screenLayout", "UNSTABLE_router", "rest", "state", "descriptors", "navigation", "NavigationContent", "createDrawerNavigator", "config"], "sourceRoot": "../../../src", "sources": ["navigators/createDrawerNavigator.tsx"], "mappings": ";;AAAA,SACEA,sBAAsB,EAGtBC,YAAY,EAMZC,oBAAoB,QACf,0BAA0B;AAQjC,SAASC,UAAU,QAAQ,wBAAqB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAEjD,SAASC,eAAeA,CAAC;EACvBC,EAAE;EACFC,gBAAgB;EAChBC,aAAa,GAAG,QAAQ;EACxBC,YAAY;EACZC,QAAQ;EACRC,MAAM;EACNC,eAAe;EACfC,aAAa;EACbC,YAAY;EACZC,eAAe;EACf,GAAGC;AACiB,CAAC,EAAE;EACvB,MAAM;IAAEC,KAAK;IAAEC,WAAW;IAAEC,UAAU;IAAEC;EAAkB,CAAC,GACzDnB,oBAAoB,CAMlBD,YAAY,EAAE;IACdM,EAAE;IACFC,gBAAgB;IAChBC,aAAa;IACbC,YAAY;IACZC,QAAQ;IACRC,MAAM;IACNC,eAAe;IACfC,aAAa;IACbC,YAAY;IACZC;EACF,CAAC,CAAC;EAEJ,oBACEX,IAAA,CAACgB,iBAAiB;IAAAV,QAAA,eAChBN,IAAA,CAACF,UAAU;MAAA,GACLc,IAAI;MACRR,aAAa,EAAEA,aAAc;MAC7BS,KAAK,EAAEA,KAAM;MACbC,WAAW,EAAEA,WAAY;MACzBC,UAAU,EAAEA;IAAW,CACxB;EAAC,CACe,CAAC;AAExB;AAEA,OAAO,SAASE,qBAAqBA,CAmBnCC,MAAe,EAAmC;EAClD,OAAOvB,sBAAsB,CAACM,eAAe,CAAC,CAACiB,MAAM,CAAC;AACxD", "ignoreList": []}