{"version": 3, "names": ["DrawerContentScrollView", "DrawerItemList", "jsx", "_jsx", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "descriptors", "state", "rest", "focusedRoute", "routes", "index", "focusedDescriptor", "key", "focusedOptions", "options", "drawerContentStyle", "drawerContentContainerStyle", "contentContainerStyle", "style", "children"], "sourceRoot": "../../../src", "sources": ["views/DrawerContent.tsx"], "mappings": ";;AACA,SAASA,uBAAuB,QAAQ,8BAA2B;AACnE,SAASC,cAAc,QAAQ,qBAAkB;AAAC,SAAAC,GAAA,IAAAC,IAAA;AAElD,OAAO,SAASC,aAAaA,CAAC;EAC5BC,WAAW;EACXC,KAAK;EACL,GAAGC;AACwB,CAAC,EAAE;EAC9B,MAAMC,YAAY,GAAGF,KAAK,CAACG,MAAM,CAACH,KAAK,CAACI,KAAK,CAAC;EAC9C,MAAMC,iBAAiB,GAAGN,WAAW,CAACG,YAAY,CAACI,GAAG,CAAC;EACvD,MAAMC,cAAc,GAAGF,iBAAiB,CAACG,OAAO;EAEhD,MAAM;IAAEC,kBAAkB;IAAEC;EAA4B,CAAC,GAAGH,cAAc;EAE1E,oBACEV,IAAA,CAACH,uBAAuB;IAAA,GAClBO,IAAI;IACRU,qBAAqB,EAAED,2BAA4B;IACnDE,KAAK,EAAEH,kBAAmB;IAAAI,QAAA,eAE1BhB,IAAA,CAACF,cAAc;MAACI,WAAW,EAAEA,WAAY;MAACC,KAAK,EAAEA,KAAM;MAAA,GAAKC;IAAI,CAAG;EAAC,CAC7C,CAAC;AAE9B", "ignoreList": []}